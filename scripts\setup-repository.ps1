# Repository Setup Script for Denis Erastus Portfolio (PowerShell)
# This script sets up the GitHub repository with proper branch strategy

param(
    [switch]$Force
)

# Set error action preference
$ErrorActionPreference = "Stop"

Write-Host "🚀 Setting up Denis Erastus Portfolio Repository..." -ForegroundColor Blue

# Function to print colored output
function Write-Status {
    param($Message)
    Write-Host "[INFO] $Message" -ForegroundColor Cyan
}

function Write-Success {
    param($Message)
    Write-Host "[SUCCESS] $Message" -ForegroundColor Green
}

function Write-Warning {
    param($Message)
    Write-Host "[WARNING] $Message" -ForegroundColor Yellow
}

function Write-Error {
    param($Message)
    Write-Host "[ERROR] $Message" -ForegroundColor Red
}

# Check if git is installed
try {
    git --version | Out-Null
    Write-Success "Git is installed"
} catch {
    Write-Error "Git is not installed. Please install Git first."
    exit 1
}

# Check if we're in a git repository
if (-not (Test-Path ".git")) {
    Write-Status "Initializing Git repository..."
    git init
    Write-Success "Git repository initialized"
}

# Set up remote origin if not exists
$remoteUrl = "https://github.com/ProDevDenis/MyPortfolio.git"
try {
    $currentRemote = git remote get-url origin 2>$null
    if ($currentRemote) {
        Write-Warning "Remote origin already exists: $currentRemote"
    }
} catch {
    Write-Status "Adding remote origin..."
    git remote add origin $remoteUrl
    Write-Success "Remote origin added: $remoteUrl"
}

# Configure Git user (if not set)
try {
    $gitUser = git config user.name 2>$null
    $gitEmail = git config user.email 2>$null
    
    if (-not $gitUser) {
        Write-Status "Setting Git user name..."
        git config user.name "Denis Erastus"
    }
    
    if (-not $gitEmail) {
        Write-Status "Setting Git email..."
        git config user.email "<EMAIL>"
    }
} catch {
    Write-Warning "Could not configure Git user settings"
}

# Create and switch to main branch
Write-Status "Setting up main branch..."
try {
    git checkout main 2>$null
} catch {
    git checkout -b main
}
Write-Success "Main branch ready"

# Add all files to staging
Write-Status "Adding files to staging..."
git add .

# Create initial commit if no commits exist
try {
    git rev-parse HEAD 2>$null | Out-Null
    Write-Warning "Repository already has commits"
} catch {
    Write-Status "Creating initial commit..."
    $commitMessage = @"
Initial commit: Project setup with Next.js, Supabase, and CI/CD

- Set up Next.js 14 with TypeScript
- Configure Tailwind CSS with custom design system
- Add Supabase integration for database operations
- Implement GitHub Actions CI/CD pipeline
- Create comprehensive documentation
- Set up development workflow and branch strategy
"@
    git commit -m $commitMessage
    Write-Success "Initial commit created"
}

# Create and switch to develop branch
Write-Status "Setting up develop branch..."
try {
    git checkout develop 2>$null
} catch {
    git checkout -b develop
}
Write-Success "Develop branch ready"

# Push both branches to remote
Write-Status "Pushing branches to remote..."
try {
    git push -u origin main
    git push -u origin develop
    Write-Success "Branches pushed to remote"
} catch {
    Write-Warning "Could not push to remote. You may need to authenticate with GitHub first."
    Write-Host "Run: git push -u origin main" -ForegroundColor Yellow
    Write-Host "Run: git push -u origin develop" -ForegroundColor Yellow
}

# Switch back to develop for development
git checkout develop

Write-Success "Repository setup complete!"
Write-Host ""
Write-Host "📋 Next Steps:" -ForegroundColor Blue
Write-Host "1. Set up branch protection rules in GitHub:"
Write-Host "   - Go to Settings > Branches"
Write-Host "   - Add protection rule for 'main' branch"
Write-Host "   - Require pull request reviews"
Write-Host "   - Require status checks to pass"
Write-Host ""
Write-Host "2. Configure GitHub Secrets for CI/CD:"
Write-Host "   - VERCEL_TOKEN"
Write-Host "   - VERCEL_ORG_ID" 
Write-Host "   - VERCEL_PROJECT_ID"
Write-Host "   - STAGING_SUPABASE_URL"
Write-Host "   - STAGING_SUPABASE_ANON_KEY"
Write-Host "   - PRODUCTION_SUPABASE_URL"
Write-Host "   - PRODUCTION_SUPABASE_ANON_KEY"
Write-Host ""
Write-Host "3. Set up Vercel deployment:"
Write-Host "   - Connect GitHub repository"
Write-Host "   - Configure environment variables"
Write-Host "   - Set up custom domain"
Write-Host ""
Write-Host "4. Create Supabase projects:"
Write-Host "   - Staging environment"
Write-Host "   - Production environment"
Write-Host "   - Run database migrations"
Write-Host ""
Write-Host "🎯 Current branch: develop" -ForegroundColor Green
Write-Host "🔄 Workflow: feature/* → develop → main" -ForegroundColor Green
Write-Host ""
Write-Success "Happy coding! 🚀"
