{"name": "denis-erastus-portfolio", "version": "1.0.0", "description": "AI Automation Expert Portfolio - <PERSON>", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "test": "echo \"No tests specified\" && exit 0", "test:watch": "echo \"No tests specified\" && exit 0", "test:coverage": "echo \"No tests specified\" && exit 0", "type-check": "tsc --noEmit", "format": "prettier --write .", "format:check": "prettier --check ."}, "dependencies": {"@supabase/supabase-js": "^2.38.5", "crypto-js": "^4.2.0", "next": "14.0.4", "react": "^18", "react-dom": "^18"}, "devDependencies": {"@testing-library/jest-dom": "^6.0.0", "@testing-library/react": "^13.0.0", "@types/crypto-js": "^4.2.2", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "autoprefixer": "^10.0.1", "eslint": "^8", "eslint-config-next": "14.0.4", "jest": "^29.0.0", "jest-environment-jsdom": "^29.0.0", "postcss": "^8", "prettier": "^3.0.0", "tailwindcss": "^3.3.0", "typescript": "^5"}, "engines": {"node": ">=18.0.0"}, "repository": {"type": "git", "url": "https://github.com/ProDevDenis/MyPortfolio.git"}, "author": "<PERSON>", "license": "MIT", "keywords": ["portfolio", "ai-automation", "next.js", "react", "typescript", "supabase"]}