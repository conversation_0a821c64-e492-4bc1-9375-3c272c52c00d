# Vercel Environment Variables Setup

## 🚀 Quick Setup Guide

### Step 1: Import Project to Vercel

1. Go to [vercel.com](https://vercel.com)
2. Click "New Project"
3. Import from GitHub: `ProDevDenis/MyPortfolio`
4. Configure project settings:
   - **Framework Preset**: Next.js
   - **Root Directory**: `./`
   - **Build Command**: `npm run build`
   - **Output Directory**: `.next`

### Step 2: Add Environment Variables

Go to your Vercel project → Settings → Environment Variables

Add these variables for **ALL environments** (Production, Preview, Development):

```env
NEXT_PUBLIC_SUPABASE_URL
Value: https://hkbhttezrkjbyoougifv.supabase.co
Environments: ✅ Production ✅ Preview ✅ Development

NEXT_PUBLIC_SUPABASE_ANON_KEY
Value: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.M_cWwOHTvSKQdyjvYyyJ1HHhbiImiGqUCfpWvUm_llc
Environments: ✅ Production ✅ Preview ✅ Development

NODE_ENV
Value: production (for Production environment only)
Value: preview (for Preview environment only)
Value: development (for Development environment only)

CONTACT_EMAIL
Value: <EMAIL>
Environments: ✅ Production ✅ Preview ✅ Development
```

### Step 3: GitHub Secrets (For CI/CD)

Go to GitHub Repository → Settings → Secrets and Variables → Actions

Add these **Repository Secrets**:

```env
# Vercel Integration
VERCEL_TOKEN=your_vercel_token_from_vercel.com/account/tokens
VERCEL_ORG_ID=your_vercel_org_id_from_project_settings
VERCEL_PROJECT_ID=your_vercel_project_id_from_project_settings

# Supabase (same values as Vercel)
STAGING_SUPABASE_URL=https://hkbhttezrkjbyoougifv.supabase.co
STAGING_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.M_cWwOHTvSKQdyjvYyyJ1HHhbiImiGqUCfpWvUm_llc

PRODUCTION_SUPABASE_URL=https://hkbhttezrkjbyoougifv.supabase.co
PRODUCTION_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.M_cWwOHTvSKQdyjvYyyJ1HHhbiImiGqUCfpWvUm_llc
```

### Step 4: Get Vercel Tokens

1. **Vercel Token**:
   - Go to [vercel.com/account/tokens](https://vercel.com/account/tokens)
   - Create new token → Copy it

2. **Vercel Org ID & Project ID**:
   - Go to your Vercel project → Settings → General
   - Copy `Project ID` and `Team ID` (if in a team) or use your username

### Step 5: Test Deployment

1. **Manual Deploy**: In Vercel dashboard, click "Deploy" to test
2. **Automatic Deploy**: Push to `develop` branch to trigger staging deployment
3. **Production Deploy**: Merge to `main` branch for production deployment

## 🔧 Troubleshooting

### Common Issues:

1. **"Missing Supabase environment variables"**
   - Ensure all environment variables are added to Vercel
   - Check that variable names match exactly
   - Verify all environments (Production, Preview, Development) are selected

2. **"Environment Variable references Secret that does not exist"**
   - Remove any `@` references in vercel.json
   - Use direct values in Vercel dashboard instead

3. **Build fails with "Cannot read properties of undefined"**
   - Check that environment variables are properly set
   - Verify Supabase URL and keys are correct

### Verification Steps:

1. ✅ Vercel project imported successfully
2. ✅ Environment variables added to all environments
3. ✅ GitHub secrets configured
4. ✅ Manual deployment successful
5. ✅ Automatic deployment working

## 🚀 Expected Results

After setup:

- **Staging URL**: `https://my-portfolio-git-develop-your-username.vercel.app`
- **Production URL**: `https://my-portfolio-your-username.vercel.app` (or custom domain)
- **Automatic deployments** on push to develop/main
- **Environment-specific configurations**

---

_Setup Guide Version: 1.0.0_
_Last Updated: 2025-01-01_
