# Database Schema - Supabase

## 📊 Database Overview

This document outlines the database schema for the Denis Erastus Portfolio application using Supabase (PostgreSQL).

## 🗄️ Tables

### 1. Contacts Table

Stores contact form submissions from the website.

```sql
CREATE TABLE contacts (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  name TEXT NOT NULL,
  email TEXT NOT NULL,
  message TEXT NOT NULL,
  source TEXT DEFAULT 'website',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes for performance
CREATE INDEX idx_contacts_created_at ON contacts(created_at DESC);
CREATE INDEX idx_contacts_email ON contacts(email);
CREATE INDEX idx_contacts_source ON contacts(source);
```

**Fields:**

- `id` - Unique identifier (UUID)
- `name` - Contact's full name
- `email` - Contact's email address
- `message` - Contact message/inquiry
- `source` - Source of contact (website, referral, etc.)
- `created_at` - Timestamp of submission

### 2. Analytics Table

Tracks page views and user behavior for custom analytics.

```sql
CREATE TABLE analytics (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  page TEXT NOT NULL,
  user_agent TEXT,
  referrer TEXT,
  ip_address INET,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes for performance
CREATE INDEX idx_analytics_created_at ON analytics(created_at DESC);
CREATE INDEX idx_analytics_page ON analytics(page);
CREATE INDEX idx_analytics_page_date ON analytics(page, created_at DESC);
```

**Fields:**

- `id` - Unique identifier (UUID)
- `page` - Page path visited
- `user_agent` - Browser user agent string
- `referrer` - Referring page URL
- `ip_address` - Visitor IP address (for analytics)
- `created_at` - Timestamp of page view

## 🔐 Row Level Security (RLS)

### Contacts Table RLS

```sql
-- Enable RLS
ALTER TABLE contacts ENABLE ROW LEVEL SECURITY;

-- Policy for inserting contacts (public can insert)
CREATE POLICY "Anyone can insert contacts" ON contacts
  FOR INSERT WITH CHECK (true);

-- Policy for reading contacts (authenticated users only)
CREATE POLICY "Authenticated users can read contacts" ON contacts
  FOR SELECT USING (auth.role() = 'authenticated');
```

### Analytics Table RLS

```sql
-- Enable RLS
ALTER TABLE analytics ENABLE ROW LEVEL SECURITY;

-- Policy for inserting analytics (public can insert)
CREATE POLICY "Anyone can insert analytics" ON analytics
  FOR INSERT WITH CHECK (true);

-- Policy for reading analytics (authenticated users only)
CREATE POLICY "Authenticated users can read analytics" ON analytics
  FOR SELECT USING (auth.role() = 'authenticated');
```

## 📈 Views and Functions

### Contact Summary View

```sql
CREATE VIEW contact_summary AS
SELECT
  DATE(created_at) as date,
  COUNT(*) as total_contacts,
  COUNT(DISTINCT email) as unique_contacts,
  source
FROM contacts
GROUP BY DATE(created_at), source
ORDER BY date DESC;
```

### Analytics Summary Function

```sql
CREATE OR REPLACE FUNCTION get_analytics_summary(days_back INTEGER DEFAULT 30)
RETURNS TABLE (
  total_views BIGINT,
  unique_pages BIGINT,
  top_pages JSON
) AS $$
BEGIN
  RETURN QUERY
  SELECT
    COUNT(*) as total_views,
    COUNT(DISTINCT page) as unique_pages,
    (
      SELECT json_agg(
        json_build_object(
          'page', page,
          'views', view_count
        )
      )
      FROM (
        SELECT page, COUNT(*) as view_count
        FROM analytics
        WHERE created_at >= NOW() - INTERVAL '1 day' * days_back
        GROUP BY page
        ORDER BY view_count DESC
        LIMIT 10
      ) top_pages_data
    ) as top_pages
  FROM analytics
  WHERE created_at >= NOW() - INTERVAL '1 day' * days_back;
END;
$$ LANGUAGE plpgsql;
```

## 🔄 Real-time Subscriptions

### Contact Notifications

```typescript
// Subscribe to new contacts
const contactSubscription = supabase
  .channel('contacts')
  .on(
    'postgres_changes',
    { event: 'INSERT', schema: 'public', table: 'contacts' },
    payload => {
      console.log('New contact:', payload.new)
      // Handle new contact notification
    }
  )
  .subscribe()
```

### Analytics Tracking

```typescript
// Subscribe to analytics events
const analyticsSubscription = supabase
  .channel('analytics')
  .on(
    'postgres_changes',
    { event: 'INSERT', schema: 'public', table: 'analytics' },
    payload => {
      console.log('New page view:', payload.new)
      // Handle real-time analytics
    }
  )
  .subscribe()
```

## 🚀 Setup Instructions

### 1. Create Supabase Project

1. Go to [supabase.com](https://supabase.com)
2. Create new project
3. Note down project URL and anon key

### 2. Run Database Migrations

Execute the SQL commands in the Supabase SQL editor:

```sql
-- Create tables
-- (Copy the CREATE TABLE statements above)

-- Set up RLS
-- (Copy the RLS policies above)

-- Create views and functions
-- (Copy the view and function definitions above)
```

### 3. Configure Environment Variables

```env
NEXT_PUBLIC_SUPABASE_URL=your_project_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key
```

## 📊 Data Management

### Backup Strategy

- Supabase provides automatic backups
- Export data regularly for additional safety
- Use Supabase CLI for local development

### Performance Optimization

- Indexes are created for frequently queried columns
- Use pagination for large datasets
- Implement caching for analytics data

## 🔍 Monitoring

### Database Metrics

Monitor through Supabase dashboard:

- Connection count
- Query performance
- Storage usage
- API requests

### Custom Monitoring

```sql
-- Monitor table sizes
SELECT
  schemaname,
  tablename,
  attname,
  n_distinct,
  correlation
FROM pg_stats
WHERE schemaname = 'public';
```

---

_Database Schema Version: 1.0.0_
_Last Updated: 2025-01-01_
