# Component Library Documentation

## 📱 Header Component

### Overview
The Header component provides responsive navigation with separate desktop and mobile experiences.

### Features
- **Responsive Design**: Separate navigation for desktop and mobile
- **Glassmorphism Styling**: Modern glass effect with backdrop blur
- **Active Section Tracking**: Highlights current section in navigation
- **Mobile Modal Navigation**: Full-screen modal with icons and text

### Navigation Structure

#### Desktop Navigation (`.desktop-nav`)
- **Display**: Horizontal navigation bar
- **Items**: Text-only links (Home, Services, Portfolio, Reviews, FAQ)
- **Styling**: Clean underline hover effects
- **Responsive**: Visible on screens ≥768px

#### Mobile Navigation (`.mobile-nav`)
- **Display**: Modal overlay with glassmorphism effect
- **Items**: Icon + text links for better mobile UX
- **Positioning**: 
  - Default: `top: 25%`, `transform: translate(-50%, 0)`
  - Small screens (<481px): `top: 20%`
- **Styling**: Dark glass panel with cyan accents

### Mobile Navigation Positioning

The mobile navigation modal is specifically positioned to ensure all navigation items are visible:

```css
/* Default mobile positioning */
.mobile-nav {
  position: fixed;
  top: 25%; /* Anchored from top, not centered */
  left: 50%;
  transform: translate(-50%, 0); /* Horizontal center only */
  max-height: 70vh;
}

/* Small screen optimization */
@media (max-width: 480px) {
  .mobile-nav {
    top: 20%; /* Higher positioning for small screens */
    max-height: 65vh;
  }
}
```

### Navigation Items

All navigation includes these 5 items:
1. **Home** - Links to hero section
2. **Services** - Links to insights/services section  
3. **Portfolio** - Links to case study section
4. **Reviews** - Links to testimonials section
5. **FAQ** - Links to FAQ section

### Responsive Behavior

| Screen Size | Navigation Type | Positioning | Features |
|-------------|----------------|-------------|----------|
| ≥768px | Desktop | Horizontal bar | Text-only links |
| <768px | Mobile Modal | `top: 25%` | Icons + text |
| <481px | Mobile Modal | `top: 20%` | Compact sizing |

### Implementation Notes

1. **Separation of Concerns**: Desktop and mobile navigation are completely separate components
2. **Accessibility**: Proper ARIA labels and keyboard navigation support
3. **Performance**: Smooth animations with hardware acceleration
4. **Touch-Friendly**: Adequate touch targets for mobile interaction

### Recent Updates

**Version 1.1.0** (2025-01-03)
- Separated desktop and mobile navigation components
- Fixed mobile navigation positioning to prevent item cutoff
- Optimized modal positioning for all screen sizes
- Improved touch targets and spacing for mobile UX

### Usage Example

```tsx
import Header from '@/components/Header'

export default function Layout() {
  return (
    <>
      <Header />
      {/* Page content */}
    </>
  )
}
```

### Styling Classes

#### Desktop Navigation
- `.desktop-nav` - Main desktop navigation container
- `.desktop-nav a` - Navigation links with hover effects

#### Mobile Navigation  
- `.mobile-nav` - Mobile modal container
- `.mobile-nav.active` - Active state with visibility
- `.mobile-nav a` - Mobile navigation links with icons
- `.nav-icon` - SVG icons for mobile navigation

### Browser Support

- **Modern Browsers**: Full support with glassmorphism effects
- **Legacy Browsers**: Graceful degradation with solid backgrounds
- **Mobile Browsers**: Optimized for touch interaction

---

_Component Documentation Version: 1.1.0_
_Last Updated: 2025-01-03_
