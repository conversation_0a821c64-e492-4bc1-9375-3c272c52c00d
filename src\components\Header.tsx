'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'

export default function Header() {
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false)
  const [activeSection, setActiveSection] = useState('hero')

  const toggleMobileMenu = () => {
    setMobileMenuOpen(!mobileMenuOpen)
  }

  const closeMobileMenu = () => {
    setMobileMenuOpen(false)
  }

  // Close mobile menu when clicking outside or on escape
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Element
      if (mobileMenuOpen && !target.closest('nav')) {
        closeMobileMenu()
      }
    }

    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && mobileMenuOpen) {
        closeMobileMenu()
      }
    }

    if (mobileMenuOpen) {
      document.addEventListener('mousedown', handleClickOutside)
      document.addEventListener('keydown', handleEscape)
      document.body.style.overflow = 'hidden'
    } else {
      document.body.style.overflow = ''
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
      document.removeEventListener('keydown', handleEscape)
      document.body.style.overflow = ''
    }
  }, [mobileMenuOpen])

  // Track active section
  useEffect(() => {
    const handleScroll = () => {
      const sections = ['hero', 'insights', 'case-study', 'testimonials', 'faq']
      const scrollPosition = window.scrollY + 100

      for (const section of sections) {
        const element = document.getElementById(section)
        if (element) {
          const offsetTop = element.offsetTop
          const offsetBottom = offsetTop + element.offsetHeight

          if (scrollPosition >= offsetTop && scrollPosition < offsetBottom) {
            setActiveSection(section)
            break
          }
        }
      }
    }

    window.addEventListener('scroll', handleScroll)
    handleScroll() // Check initial position

    return () => window.removeEventListener('scroll', handleScroll)
  }, [])

  const openContactModal = () => {
    const modal = document.getElementById('contactModal')
    if (modal) {
      modal.classList.add('active')
      document.body.style.overflow = 'hidden'
    }
    closeMobileMenu() // Close mobile menu when opening contact modal
  }

  return (
    <>
      <header>
        <nav className="container">
          <Link href="#hero" className="logo">
            DENIS
          </Link>

          {/* Desktop Navigation */}
          <ul className="nav-links desktop-nav">
            <li>
              <Link
                href="#hero"
                className={activeSection === 'hero' ? 'active' : ''}
              >
                Home
              </Link>
            </li>
            <li>
              <Link
                href="#insights"
                className={activeSection === 'insights' ? 'active' : ''}
              >
                Services
              </Link>
            </li>
            <li>
              <Link
                href="#case-study"
                className={activeSection === 'case-study' ? 'active' : ''}
              >
                Portfolio
              </Link>
            </li>
            <li>
              <Link
                href="#testimonials"
                className={activeSection === 'testimonials' ? 'active' : ''}
              >
                Reviews
              </Link>
            </li>
            <li>
              <Link
                href="#faq"
                className={activeSection === 'faq' ? 'active' : ''}
              >
                FAQ
              </Link>
            </li>
          </ul>

          {/* Mobile Navigation */}
          <ul
            className={`nav-links mobile-nav ${mobileMenuOpen ? 'active' : ''}`}
            id="navMenu"
          >
            <li>
              <Link
                href="#hero"
                onClick={closeMobileMenu}
                className={activeSection === 'hero' ? 'active' : ''}
              >
                <svg
                  className="nav-icon"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z" />
                </svg>
                Home
              </Link>
            </li>
            <li>
              <Link
                href="#insights"
                onClick={closeMobileMenu}
                className={activeSection === 'insights' ? 'active' : ''}
              >
                <svg
                  className="nav-icon"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path
                    fillRule="evenodd"
                    d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z"
                    clipRule="evenodd"
                  />
                </svg>
                Services
              </Link>
            </li>
            <li>
              <Link
                href="#case-study"
                onClick={closeMobileMenu}
                className={activeSection === 'case-study' ? 'active' : ''}
              >
                <svg
                  className="nav-icon"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path
                    fillRule="evenodd"
                    d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z"
                    clipRule="evenodd"
                  />
                </svg>
                Portfolio
              </Link>
            </li>
            <li>
              <Link
                href="#testimonials"
                onClick={closeMobileMenu}
                className={activeSection === 'testimonials' ? 'active' : ''}
              >
                <svg
                  className="nav-icon"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path
                    fillRule="evenodd"
                    d="M18 10c0 3.866-3.582 7-8 7a8.841 8.841 0 01-4.083-.98L2 17l1.338-3.123C2.493 12.767 2 11.434 2 10c0-3.866 3.582-7 8-7s8 3.134 8 7zM7 9H5v2h2V9zm8 0h-2v2h2V9zM9 9h2v2H9V9z"
                    clipRule="evenodd"
                  />
                </svg>
                Reviews
              </Link>
            </li>
            <li>
              <Link
                href="#faq"
                onClick={closeMobileMenu}
                className={activeSection === 'faq' ? 'active' : ''}
              >
                <svg
                  className="nav-icon"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path
                    fillRule="evenodd"
                    d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-3a1 1 0 00-.867.5 1 1 0 11-1.731-1A3 3 0 0113 8a3.001 3.001 0 01-2 2.83V11a1 1 0 11-2 0v-1a1 1 0 011-1 1 1 0 100-2zm0 8a1 1 0 100-2 1 1 0 000 2z"
                    clipRule="evenodd"
                  />
                </svg>
                FAQ
              </Link>
            </li>
          </ul>

          <div className="nav-cta">
            <button
              className="btn btn-primary"
              onClick={openContactModal}
              id="getInTouchNavBtn"
            >
              Get In Touch
            </button>
          </div>

          <button
            className={`mobile-menu ${mobileMenuOpen ? 'active' : ''}`}
            id="mobileMenu"
            onClick={toggleMobileMenu}
            aria-label="Toggle mobile menu"
          >
            <span></span>
            <span></span>
            <span></span>
          </button>
        </nav>
      </header>

      {/* Mobile menu overlay */}
      <div
        className={`mobile-menu-overlay ${mobileMenuOpen ? 'active' : ''}`}
        onClick={closeMobileMenu}
      />
    </>
  )
}
