/**
 * Service Worker for Background Notifications
 *
 * Handles background notifications, push messages, and offline functionality
 * for the portfolio website.
 */

const CACHE_NAME = 'portfolio-system-v1'
const urlsToCache = [
  '/',
  '/favicon.ico',
  '/manifest.json'
]

// Install event - cache resources
self.addEventListener('install', (event) => {
  console.log('Service Worker installing...')
  
  event.waitUntil(
    caches.open(CACHE_NAME)
      .then((cache) => {
        console.log('Opened cache')
        return cache.addAll(urlsToCache)
      })
      .catch((error) => {
        console.error('Cache installation failed:', error)
      })
  )
})

// Activate event - clean up old caches
self.addEventListener('activate', (event) => {
  console.log('Service Worker activating...')
  
  event.waitUntil(
    caches.keys().then((cacheNames) => {
      return Promise.all(
        cacheNames.map((cacheName) => {
          if (cacheName !== CACHE_NAME) {
            console.log('Deleting old cache:', cacheName)
            return caches.delete(cacheName)
          }
        })
      )
    })
  )
})

// Fetch event - serve from cache when offline
self.addEventListener('fetch', (event) => {
  // Only handle GET requests
  if (event.request.method !== 'GET') {
    return
  }

  event.respondWith(
    caches.match(event.request)
      .then((response) => {
        // Return cached version or fetch from network
        return response || fetch(event.request)
      })
      .catch(() => {
        // Return offline page for navigation requests
        if (event.request.mode === 'navigate') {
          return caches.match('/')
        }
      })
  )
})

// Push event - handle push notifications
self.addEventListener('push', (event) => {
  console.log('Push message received:', event)

  let notificationData = {
    title: 'New Booking',
    body: 'You have a new booking!',
    icon: '/favicon.ico',
    badge: '/favicon.ico',
    tag: 'booking-notification',
    requireInteraction: true,
    actions: [
      {
        action: 'view',
        title: 'View Booking',
        icon: '/icons/view.png'
      },
      {
        action: 'dismiss',
        title: 'Dismiss',
        icon: '/icons/dismiss.png'
      }
    ],
    data: {
      url: '/admin/bookings'
    }
  }

  // Parse push data if available
  if (event.data) {
    try {
      const pushData = event.data.json()
      notificationData = { ...notificationData, ...pushData }
    } catch (error) {
      console.error('Error parsing push data:', error)
    }
  }

  event.waitUntil(
    self.registration.showNotification(notificationData.title, notificationData)
  )
})

// Notification click event
self.addEventListener('notificationclick', (event) => {
  console.log('Notification clicked:', event)

  event.notification.close()

  const action = event.action
  const notificationData = event.notification.data || {}

  if (action === 'view' || !action) {
    // Open the app or focus existing window
    event.waitUntil(
      clients.matchAll({ type: 'window' }).then((clientList) => {
        // Check if app is already open
        for (const client of clientList) {
          if (client.url.includes(notificationData.url || '/') && 'focus' in client) {
            return client.focus()
          }
        }

        // Open new window if app is not open
        if (clients.openWindow) {
          return clients.openWindow(notificationData.url || '/')
        }
      })
    )
  } else if (action === 'dismiss') {
    // Just close the notification (already handled above)
    console.log('Notification dismissed')
  }
})

// Background sync event (for offline form submissions)
self.addEventListener('sync', (event) => {
  console.log('Background sync triggered:', event.tag)

  if (event.tag === 'contact-sync') {
    event.waitUntil(syncContacts())
  }
})

// Sync pending contacts when back online
async function syncContacts() {
  try {
    // Get pending contacts from IndexedDB or localStorage
    const pendingContacts = await getPendingContacts()

    for (const contact of pendingContacts) {
      try {
        const response = await fetch('/api/contact', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(contact)
        })

        if (response.ok) {
          // Remove from pending list
          await removePendingContact(contact.id)
          console.log('Synced contact:', contact.id)
        }
      } catch (error) {
        console.error('Failed to sync contact:', contact.id, error)
      }
    }
  } catch (error) {
    console.error('Background sync failed:', error)
  }
}

// Helper functions for offline storage
async function getPendingContacts() {
  // In a real implementation, you'd use IndexedDB
  // For now, return empty array
  return []
}

async function removePendingContact(contactId) {
  // Remove contact from offline storage
  console.log('Removing pending contact:', contactId)
}

// Message event - handle messages from main thread
self.addEventListener('message', (event) => {
  console.log('Service Worker received message:', event.data)

  if (event.data && event.data.type === 'SKIP_WAITING') {
    self.skipWaiting()
  }
})

// Error event
self.addEventListener('error', (event) => {
  console.error('Service Worker error:', event.error)
})

// Unhandled rejection event
self.addEventListener('unhandledrejection', (event) => {
  console.error('Service Worker unhandled rejection:', event.reason)
})
