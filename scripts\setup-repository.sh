#!/bin/bash

# Repository Setup Script for Denis Erast<PERSON> Portfolio
# This script sets up the GitHub repository with proper branch strategy

set -e

echo "🚀 Setting up Denis Erastus Portfolio Repository..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if git is installed
if ! command -v git &> /dev/null; then
    print_error "Git is not installed. Please install Git first."
    exit 1
fi

# Check if we're in a git repository
if [ ! -d ".git" ]; then
    print_status "Initializing Git repository..."
    git init
    print_success "Git repository initialized"
fi

# Set up remote origin if not exists
REMOTE_URL="https://github.com/ProDevDenis/MyPortfolio.git"
if ! git remote get-url origin &> /dev/null; then
    print_status "Adding remote origin..."
    git remote add origin $REMOTE_URL
    print_success "Remote origin added: $REMOTE_URL"
else
    print_warning "Remote origin already exists"
fi

# Create and switch to main branch
print_status "Setting up main branch..."
git checkout -b main 2>/dev/null || git checkout main
print_success "Main branch ready"

# Add all files to staging
print_status "Adding files to staging..."
git add .

# Create initial commit if no commits exist
if ! git rev-parse HEAD &> /dev/null; then
    print_status "Creating initial commit..."
    git commit -m "Initial commit: Project setup with Next.js, Supabase, and CI/CD

- Set up Next.js 14 with TypeScript
- Configure Tailwind CSS with custom design system
- Add Supabase integration for database operations
- Implement GitHub Actions CI/CD pipeline
- Create comprehensive documentation
- Set up development workflow and branch strategy"
    print_success "Initial commit created"
else
    print_warning "Repository already has commits"
fi

# Create and switch to develop branch
print_status "Setting up develop branch..."
git checkout -b develop 2>/dev/null || git checkout develop
print_success "Develop branch ready"

# Push both branches to remote
print_status "Pushing branches to remote..."
git push -u origin main
git push -u origin develop
print_success "Branches pushed to remote"

# Switch back to develop for development
git checkout develop

print_success "Repository setup complete!"
echo ""
echo "📋 Next Steps:"
echo "1. Set up branch protection rules in GitHub:"
echo "   - Go to Settings > Branches"
echo "   - Add protection rule for 'main' branch"
echo "   - Require pull request reviews"
echo "   - Require status checks to pass"
echo ""
echo "2. Configure GitHub Secrets for CI/CD:"
echo "   - VERCEL_TOKEN"
echo "   - VERCEL_ORG_ID" 
echo "   - VERCEL_PROJECT_ID"
echo "   - STAGING_SUPABASE_URL"
echo "   - STAGING_SUPABASE_ANON_KEY"
echo "   - PRODUCTION_SUPABASE_URL"
echo "   - PRODUCTION_SUPABASE_ANON_KEY"
echo ""
echo "3. Set up Vercel deployment:"
echo "   - Connect GitHub repository"
echo "   - Configure environment variables"
echo "   - Set up custom domain"
echo ""
echo "4. Create Supabase projects:"
echo "   - Staging environment"
echo "   - Production environment"
echo "   - Run database migrations"
echo ""
echo "🎯 Current branch: develop"
echo "🔄 Workflow: feature/* → develop → main"
echo ""
print_success "Happy coding! 🚀"
